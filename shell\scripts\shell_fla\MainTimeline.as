package shell_fla
{
   import com.junkbyte.console.Cc;
   import flash.display.Loader;
   import flash.display.MovieClip;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.events.IOErrorEvent;
   import flash.net.LocalConnection;
   import flash.net.URLRequest;
   import flash.net.URLRequestMethod;
   import flash.net.URLLoader;
   import flash.net.FileReference;
   import flash.system.Security;
   import flash.utils.getDefinitionByName;
   import flash.utils.Timer;
   import flash.events.TimerEvent;

   public dynamic class MainTimeline extends MovieClip
   {
      public var conn:LocalConnection;

      public var m_l:Loader;

      public var XingLingFactory:Class;

      // 新增：缓存常用的游戏类引用
      private var m_gamingUI:Class;
      private var m_xmlSingle:Class;
      private var m_myFunction:Class;
      private var m_myFunction2:Class;

      // 调试标志
      private var debugMode:Boolean = true;
      private var gameLoaded:Boolean = false;

      public function MainTimeline()
      {
         super();
         addFrameScript(0,this.frame1);
         debugLog("MainTimeline 构造函数执行完成");


      }


      
      public function __(param1:Event) : void
      {
         debugLog("游戏SWF加载完成，开始初始化");
         this.addChild(this.m_l);
         gameLoaded = true;

         // 延迟初始化游戏类，确保游戏完全加载
         setTimeout(function():void {
            initGameClasses();
            debugLog("游戏类初始化完成，准备连接LocalConnection");
         }, 1000);

         stage.addEventListener(MouseEvent.CLICK,this.___);
      }

      public function ___(param1:Event) : void
      {
         debugLog("尝试连接LocalConnection");
         try {
            this.conn.connect("my_cons");
            debugLog("LocalConnection连接成功");
         } catch (e:Error) {
            debugLog("LocalConnection连接失败: " + e.message);
         }
         stage.removeEventListener(MouseEvent.CLICK,this.___);
      }

      // 调试日志函数
      private function debugLog(message:String):void
      {
         if (debugMode) {
            trace("[DEBUG] " + new Date().toTimeString() + " - " + message);
            Cc.log("[DEBUG] " + message);
         }
      }

      // 延迟执行函数
      private function setTimeout(func:Function, delay:int):void
      {
         var timer:Timer = new Timer(delay, 1);
         timer.addEventListener(TimerEvent.TIMER, function(e:TimerEvent):void {
            func();
            timer.stop();
         });
         timer.start();
      }
      
      public function save(param1:Object) : void
      {
         debugLog("=== 开始执行外挂功能 ===");
         debugLog("接收到的参数: " + JSON.stringify(param1));



         // 检查游戏是否已加载
         if (!gameLoaded) {
            debugLog("错误：游戏尚未加载完成");
            return;
         }

         // 获取玩家编号
         var playerIndex:int = param1["P"];
         var functionType:int = param1["type"];
         var itemId:String = param1["id"];
         var itemNum:String = param1["num"];

         debugLog("玩家编号: " + playerIndex);
         debugLog("功能类型: " + functionType);
         debugLog("物品ID: " + itemId);
         debugLog("数量: " + itemNum);

         // 初始化游戏类引用
         if (!initGameClasses()) {
            debugLog("错误：游戏类初始化失败");
            return;
         }

         // 获取玩家对象
         var player:Object = getPlayerObject(playerIndex);
         if (!player) {
            debugLog("错误：无法获取玩家对象");
            return;
         }

         debugLog("成功获取玩家对象，开始分析玩家数据结构");
         analyzePlayerStructure(player);

         // 执行对应的修改功能
         debugLog("开始执行功能类型: " + functionType);

         try {
            switch(functionType)
            {
               case 1: // 添加武器装备
                  addEquipmentFunction(player, parseInt(itemId), parseInt(itemNum));
                  break;
               case 2: // 清理武器装备背包
                  clearPackageFunction(player, "equipment");
                  break;
               case 3: // 添加衣服装备
                  addClothesFunction(player, parseInt(itemId), parseInt(itemNum));
                  break;
               case 4: // 清理衣服装备背包
                  clearPackageFunction(player, "clothes");
                  break;
               case 5: // 添加项链装备
                  addNecklaceFunction(player, parseInt(itemId), parseInt(itemNum));
                  break;
               case 6: // 清理项链装备背包
                  clearPackageFunction(player, "necklace");
                  break;
               case 7: // 添加葫芦装备
                  addGourdFunction(player, parseInt(itemId), parseInt(itemNum));
                  break;
               case 8: // 清理葫芦装备背包
                  clearPackageFunction(player, "gourd");
                  break;
               case 9: // 添加宝石
                  addGemFunction(player, parseInt(itemId), parseInt(itemNum));
                  break;
               case 10: // 清理宝石背包
                  clearPackageFunction(player, "gem");
                  break;
               case 11: // 添加消耗品
                  addConsumableFunction(player, parseInt(itemId), parseInt(itemNum));
                  break;
               case 12: // 清理消耗品背包
                  clearPackageFunction(player, "consumable");
                  break;
               case 13: // 添加其他道具
                  addOtherItemFunction(player, parseInt(itemId), parseInt(itemNum));
                  break;
               case 14: // 清理其他道具背包
                  clearPackageFunction(player, "other");
                  break;
               case 15: // 添加任务道具
                  addQuestItemFunction(player, parseInt(itemId), parseInt(itemNum));
                  break;
               case 16: // 清理任务道具背包
                  clearPackageFunction(player, "quest");
                  break;
               case 17: // 添加宠物
                  addPetFunction(player, parseInt(itemId), parseInt(itemNum));
                  break;
               case 18: // 添加宠物装备
                  addPetEquipmentFunction(player, parseInt(itemId), parseInt(itemNum));
                  break;
               case 19: // 修改金币
                  modifyMoneyFunction(player, parseInt(itemNum));
                  break;
               case 20: // 修改等级
                  modifyLevelFunction(player, parseInt(itemNum));
                  break;
               case 21: // 修改经验
                  modifyExperienceFunction(player, parseInt(itemNum));
                  break;
               case 22: // 修复背包
                  repairPackageFunction(player);
                  break;
               case 23: // 列出可用装备ID
                  listAvailableEquipmentIds();
                  break;
               case 24: // 输出所有装备ID到控制台
                  exportAllEquipmentIds();
                  break;
               case 25: // 输出常用装备ID
                  outputSampleEquipmentIds();
                  break;
               case 26: // 修改PK点
                  modifyPKPointFunction(player, parseInt(itemNum));
                  break;
               case 27: // 打开PK界面查看PK点
                  openPKInterfaceFunction(player);
                  break;
               case 28: // 解锁所有地图关卡
                  unlockAllMapsFunction(player);
                  break;
               default:
                  debugLog("功能类型 " + functionType + " 暂未实现或不支持");
                  break;
            }
            debugLog("功能执行完成");
         } catch (e:Error) {
            debugLog("功能执行失败: " + e.message);
            debugLog("错误堆栈: " + e.getStackTrace());
         }

         debugLog("=== 外挂功能执行结束 ===");
      }



















      // 新增：初始化游戏类引用
      private function initGameClasses():Boolean
      {
         debugLog("开始初始化游戏类引用");

         try {
            // 检查加载器状态
            if (!this.m_l || !this.m_l.contentLoaderInfo) {
               debugLog("错误：游戏加载器未准备好");
               return false;
            }

            var appDomain:Object = this.m_l.contentLoaderInfo.applicationDomain;
            if (!appDomain) {
               debugLog("错误：无法获取应用程序域");
               return false;
            }

            debugLog("应用程序域获取成功，开始加载类定义");

            // 尝试获取GamingUI类
            if (!m_gamingUI) {
               try {
                  m_gamingUI = appDomain.getDefinition("UI.GamingUI") as Class;
                  debugLog("GamingUI类加载成功");
               } catch (e:Error) {
                  debugLog("GamingUI类加载失败: " + e.message);
                  return false;
               }
            }

            // 尝试获取XMLSingle类
            if (!m_xmlSingle) {
               try {
                  m_xmlSingle = appDomain.getDefinition("UI.XMLSingle") as Class;
                  debugLog("XMLSingle类加载成功");
               } catch (e:Error) {
                  debugLog("XMLSingle类加载失败: " + e.message);
                  return false;
               }
            }

            // 尝试获取MyFunction类
            if (!m_myFunction) {
               try {
                  m_myFunction = appDomain.getDefinition("UI.MyFunction") as Class;
                  debugLog("MyFunction类加载成功");
               } catch (e:Error) {
                  debugLog("MyFunction类加载失败: " + e.message);
                  return false;
               }
            }

            debugLog("所有游戏类初始化成功");
            return true;

         } catch (e:Error) {
            debugLog("初始化游戏类失败: " + e.message);
            debugLog("错误堆栈: " + e.getStackTrace());
            return false;
         }
      }

      // 新增：获取玩家对象
      private function getPlayerObject(playerIndex:int):Object
      {
         debugLog("尝试获取玩家对象，玩家编号: " + playerIndex);

         try {
            if (!m_gamingUI) {
               debugLog("错误：GamingUI类未初始化");
               return null;
            }

            var gamingUI:Object = m_gamingUI["getInstance"]();
            if (!gamingUI) {
               debugLog("错误：无法获取GamingUI实例");
               return null;
            }

            debugLog("GamingUI实例获取成功");

            var player:Object = null;
            if (playerIndex == 1) {
               player = gamingUI.player1;
               debugLog("尝试获取player1");
            } else if (playerIndex == 2) {
               player = gamingUI.player2;
               debugLog("尝试获取player2");
            } else {
               debugLog("错误：无效的玩家编号: " + playerIndex);
               return null;
            }

            if (player) {
               debugLog("玩家对象获取成功");
               return player;
            } else {
               debugLog("错误：玩家对象为null");
               return null;
            }

         } catch (e:Error) {
            debugLog("获取玩家对象失败: " + e.message);
            debugLog("错误堆栈: " + e.getStackTrace());
         }
         return null;
      }

      // 新增：分析玩家数据结构
      private function analyzePlayerStructure(player:Object):void
      {
         debugLog("=== 开始分析玩家数据结构 ===");

         try {
            if (!player) {
               debugLog("玩家对象为null");
               return;
            }

            debugLog("玩家对象类型: " + typeof(player));
            debugLog("玩家对象类名: " + Object(player).constructor);

            // 分析playerVO
            if (player.playerVO) {
               debugLog("playerVO存在");
               debugLog("playerVO类型: " + typeof(player.playerVO));

               // 检查常用属性
               var commonProps:Array = ["money", "level", "experienceVolume", "bloodPercent", "magicPercent"];
               for each (var prop:String in commonProps) {
                  try {
                     var value:* = player.playerVO[prop];
                     debugLog("playerVO." + prop + " = " + value + " (类型: " + typeof(value) + ")");
                  } catch (e:Error) {
                     debugLog("无法访问playerVO." + prop + ": " + e.message);
                  }
               }

               // 检查_antiwear属性
               if (player.playerVO._antiwear) {
                  debugLog("playerVO._antiwear存在");
                  var antiwearProps:Array = ["baseBloodVolume", "baseMaxMagic", "baseAttack", "baseDefence"];
                  for each (var aProp:String in antiwearProps) {
                     try {
                        var aValue:* = player.playerVO._antiwear[aProp];
                        debugLog("playerVO._antiwear." + aProp + " = " + aValue);
                     } catch (e:Error) {
                        debugLog("无法访问playerVO._antiwear." + aProp + ": " + e.message);
                     }
                  }
               } else {
                  debugLog("playerVO._antiwear不存在");
               }

               // 检查背包
               if (player.playerVO.packageEquipmentVOs) {
                  debugLog("背包存在，长度: " + player.playerVO.packageEquipmentVOs.length);
               } else {
                  debugLog("背包不存在");
               }

            } else {
               debugLog("playerVO不存在");
            }

         } catch (e:Error) {
            debugLog("分析玩家数据结构失败: " + e.message);
         }

         debugLog("=== 玩家数据结构分析完成 ===");
      }

      // 新增：创建装备
      private function createEquipment(equipmentId:int):Object
      {
         debugLog("尝试创建装备，ID: " + equipmentId);

         try {
            if (!m_xmlSingle) {
               debugLog("错误：XMLSingle类未初始化");
               return null;
            }

            var xmlSingle:Object = m_xmlSingle["getInstance"]();
            if (!xmlSingle) {
               debugLog("错误：无法获取XMLSingle实例");
               return null;
            }

            debugLog("XMLSingle实例获取成功");

            // 检查equipmentXML是否存在
            if (!xmlSingle.equipmentXML) {
               debugLog("错误：equipmentXML不存在");
               return null;
            }

            debugLog("equipmentXML存在，开始创建装备");

            // 使用正确的静态方法调用
            var equipment:Object = m_xmlSingle["getEquipmentVOByID"](equipmentId, xmlSingle.equipmentXML, null, false);
            if (equipment) {
               debugLog("装备创建成功，类型: " + typeof(equipment));
               debugLog("装备名称: " + equipment.name);
               debugLog("装备等级: " + equipment.level);
               return equipment;
            } else {
               debugLog("装备创建失败，返回null");
               return null;
            }

         } catch (e:Error) {
            debugLog("创建装备失败: " + e.message);
            debugLog("错误堆栈: " + e.getStackTrace());

            // 尝试备用方法1：使用getEquipment方法
            debugLog("尝试备用创建方法1");
            try {
               var equipment2:Object = m_xmlSingle["getEquipment"](equipmentId, xmlSingle.equipmentXML);
               if (equipment2) {
                  debugLog("备用方法1创建装备成功");
                  return equipment2;
               }
            } catch (e2:Error) {
               debugLog("备用方法1也失败: " + e2.message);
            }

            // 尝试备用方法2：使用getBaseEquipment方法
            debugLog("尝试备用创建方法2");
            try {
               var equipment3:Object = m_xmlSingle["getBaseEquipment"](equipmentId, xmlSingle.equipmentXML);
               if (equipment3) {
                  debugLog("备用方法2创建装备成功");
                  return equipment3;
               }
            } catch (e3:Error) {
               debugLog("备用方法2也失败: " + e3.message);
            }
         }
         return null;
      }

      // 新增：添加装备到背包
      private function addEquipmentToPackage(player:Object, equipment:Object):Boolean
      {
         debugLog("尝试添加装备到背包");

         try {
            if (!player || !player.playerVO) {
               debugLog("错误：玩家或playerVO对象无效");
               return false;
            }

            if (!equipment) {
               debugLog("错误：装备对象无效");
               return false;
            }

            // 检查背包是否存在
            if (!player.playerVO.packageEquipmentVOs) {
               debugLog("错误：背包不存在");
               return false;
            }

            debugLog("背包存在，当前长度: " + player.playerVO.packageEquipmentVOs.length);
            debugLog("装备信息 - ID: " + equipment.id + ", 名称: " + equipment.name + ", 类型: " + equipment.equipmentType);

            // 尝试使用MyFunction添加装备
            if (m_myFunction) {
               try {
                  var myFunction:Object = m_myFunction["getInstance"]();
                  if (myFunction && myFunction["putInEquipmentVOToEquipmentVOVector"]) {
                     // 使用正确的参数：背包数组, 装备对象, 模式(0=正常), 保留位置(0=无)
                     var result:Boolean = myFunction["putInEquipmentVOToEquipmentVOVector"](
                        player.playerVO.packageEquipmentVOs,
                        equipment,
                        0,  // 模式参数
                        0   // 保留位置参数
                     );
                     debugLog("通过MyFunction添加装备结果: " + result);
                     if (result) {
                        return true;
                     }
                  } else {
                     debugLog("MyFunction方法不存在");
                  }
               } catch (e1:Error) {
                  debugLog("通过MyFunction添加装备失败: " + e1.message);
                  debugLog("MyFunction错误堆栈: " + e1.getStackTrace());
               }
            } else {
               debugLog("MyFunction类未初始化");
            }

            // 备用方案：直接添加到背包
            debugLog("尝试直接添加到背包");
            try {
               // 查找空位
               for (var i:int = 0; i < player.playerVO.packageEquipmentVOs.length; i++) {
                  if (player.playerVO.packageEquipmentVOs[i] == null) {
                     player.playerVO.packageEquipmentVOs[i] = equipment;
                     debugLog("直接添加装备到位置: " + i);
                     return true;
                  }
               }
               debugLog("背包已满，无法添加装备");
               return false;
            } catch (e2:Error) {
               debugLog("直接添加装备失败: " + e2.message);
               return false;
            }

         } catch (e:Error) {
            debugLog("添加装备到背包失败: " + e.message);
            debugLog("错误堆栈: " + e.getStackTrace());
         }
         return false;
      }

      // 新增：检查装备ID是否有效
      private function isValidEquipmentId(equipmentId:int):Boolean
      {
         try {
            // 首先检查是否在反作弊黑名单中
            if (isBlacklistedEquipmentId(equipmentId)) {
               debugLog("装备ID " + equipmentId + " 在反作弊黑名单中，拒绝添加");
               return false;
            }

            if (!m_xmlSingle) {
               return false;
            }

            var xmlSingle:Object = m_xmlSingle["getInstance"]();
            if (!xmlSingle || !xmlSingle.equipmentXML) {
               return false;
            }

            // 检查装备ID是否存在于XML中
            var equipmentXML:XML = xmlSingle.equipmentXML;
            var equipmentNode:XMLList = equipmentXML.item.(@id == equipmentId);

            if (equipmentNode.length() > 0) {
               debugLog("装备ID " + equipmentId + " 有效，名称: " + equipmentNode[0].@name);
               return true;
            } else {
               debugLog("装备ID " + equipmentId + " 无效，在XML中未找到");

               // 提供一些有效的装备ID建议
               debugLog("建议使用以下有效的装备ID:");
               debugLog("基础武器: 10101001-10101010 (金箍棒系列)");
               debugLog("基础防具: 10201001-10201010 (布衣系列)");
               debugLog("基础饰品: 10301001-10301010 (戒指系列)");
               debugLog("消耗品: 20001-20013 (血瓶蓝瓶系列)");
               debugLog("材料: 10500001-10500030 (基础材料)");

               return false;
            }

         } catch (e:Error) {
            debugLog("检查装备ID失败: " + e.message);
            return false;
         }
      }

      // 新增：列出可用的装备ID（调试用）
      private function listAvailableEquipmentIds():void
      {
         try {
            if (!m_xmlSingle) {
               debugLog("XMLSingle未初始化");
               return;
            }

            var xmlSingle:Object = m_xmlSingle["getInstance"]();
            if (!xmlSingle || !xmlSingle.equipmentXML) {
               debugLog("equipmentXML不可用");
               return;
            }

            var equipmentXML:XML = xmlSingle.equipmentXML;
            var items:XMLList = equipmentXML.item;

            debugLog("=== 可用装备ID列表 (前20个) ===");
            var count:int = 0;
            for (var i:int = 0; i < items.length() && count < 20; i++) {
               var item:XML = items[i];
               var id:String = item.@id;
               var name:String = item.@name;
               if (id && name) {
                  debugLog("ID: " + id + " - " + name);
                  count++;
               }
            }
            debugLog("=== 装备ID列表结束 ===");

         } catch (e:Error) {
            debugLog("列出装备ID失败: " + e.message);
         }
      }

      // 新增：导出所有装备ID到桌面txt文件
      private function exportAllEquipmentIds():void
      {
         try {
            if (!m_xmlSingle) {
               debugLog("XMLSingle未初始化，无法导出装备ID");
               return;
            }

            var xmlSingle:Object = m_xmlSingle["getInstance"]();
            if (!xmlSingle || !xmlSingle.equipmentXML) {
               debugLog("equipmentXML不可用，无法导出装备ID");
               return;
            }

            var equipmentXML:XML = xmlSingle.equipmentXML;
            var items:XMLList = equipmentXML.item;

            debugLog("开始导出装备ID，总数量: " + items.length());

            // 构建导出内容
            var exportContent:String = "《西游大战僵尸2》装备ID完整列表\n";
            exportContent += "导出时间: " + new Date().toString() + "\n";
            exportContent += "总装备数量: " + items.length() + "\n";
            exportContent += "格式: 装备ID - 装备名称 - 装备类型 - 等级\n";
            exportContent += "=" * 60 + "\n\n";

            var validCount:int = 0;
            var categoryMap:Object = {};

            // 遍历所有装备
            for (var i:int = 0; i < items.length(); i++) {
               var item:XML = items[i];
               var id:String = item.@id;
               var name:String = item.@name;
               var equipType:String = item.@equipmentType || "未知";
               var level:String = item.@level || "1";
               var className:String = item.@className || "";

               if (id && name) {
                  validCount++;

                  // 按类型分类
                  if (!categoryMap[equipType]) {
                     categoryMap[equipType] = [];
                  }
                  categoryMap[equipType].push({
                     id: id,
                     name: name,
                     level: level,
                     className: className
                  });
               }
            }

            // 按分类输出
            for (var category:String in categoryMap) {
               exportContent += "\n【" + category + "】\n";
               exportContent += "-" * 30 + "\n";

               var categoryItems:Array = categoryMap[category];
               for (var j:int = 0; j < categoryItems.length; j++) {
                  var equipItem:Object = categoryItems[j];
                  exportContent += equipItem.id + " - " + equipItem.name + " - 等级:" + equipItem.level;
                  if (equipItem.className) {
                     exportContent += " - 类名:" + equipItem.className;
                  }
                  exportContent += "\n";
               }
            }

            exportContent += "\n" + "=" * 60 + "\n";
            exportContent += "有效装备总数: " + validCount + "/" + items.length() + "\n";
            exportContent += "导出完成时间: " + new Date().toString() + "\n";

            // 直接弹出文件保存对话框
            saveFileWithDialog(exportContent);

            debugLog("装备ID导出完成！");
            debugLog("文件保存对话框已打开，请选择保存位置");
            debugLog("有效装备数量: " + validCount + "/" + items.length());
            debugLog("建议保存到桌面方便查找");

         } catch (e:Error) {
            debugLog("导出装备ID失败: " + e.message);
            debugLog("错误堆栈: " + e.getStackTrace());
         }
      }

      // 新增：智能保存方案
      private function saveFileWithDialog(content:String):void
      {
         debugLog("开始智能保存装备ID列表...");

         // 检测Flash Player版本和权限
         try {
            var capabilities:Object = getDefinitionByName("flash.system.Capabilities");
            var playerType:String = capabilities["playerType"];
            debugLog("Flash Player类型: " + playerType);

            // 如果是浏览器环境，直接使用备用方案
            if (playerType == "PlugIn" || playerType == "ActiveX") {
               debugLog("检测到浏览器环境，使用备用输出方案");
               outputEquipmentIdsByCategory(content);
               return;
            }
         } catch (e:Error) {
            debugLog("无法检测Flash Player环境: " + e.message);
         }

         // 尝试文件保存
         try {
            debugLog("尝试打开文件保存对话框...");

            var fileRef:FileReference = new FileReference();

            // 监听保存事件
            fileRef.addEventListener(Event.SELECT, function(e:Event):void {
               debugLog("✅ 用户选择了保存位置");
            });

            fileRef.addEventListener(Event.COMPLETE, function(e:Event):void {
               debugLog("🎉 文件保存完成！");
               debugLog("📁 装备ID列表已成功保存");
               debugLog("💡 现在可以打开文件查看所有装备ID");
            });

            fileRef.addEventListener(Event.CANCEL, function(e:Event):void {
               debugLog("❌ 用户取消了保存");
               debugLog("💡 如需保存，请重新执行功能26");
               debugLog("🔧 或者查看下方输出的分类装备ID:");
               outputEquipmentIdsByCategory(content);
            });

            fileRef.addEventListener(IOErrorEvent.IO_ERROR, function(e:IOErrorEvent):void {
               debugLog("❌ 文件保存失败: " + e.text);
               debugLog("🔧 改为分类输出装备ID:");
               outputEquipmentIdsByCategory(content);
            });

            // 尝试保存文件
            fileRef.save(content, "西游大战僵尸2_装备ID列表.txt");
            debugLog("📂 文件保存对话框已打开");
            debugLog("💾 请选择保存位置（建议桌面）");

         } catch (e:Error) {
            debugLog("❌ 文件保存功能被禁用 (Error #" + e.errorID + ": " + e.message + ")");

            if (e.errorID == 2176) {
               debugLog("🔧 Error #2176 解决方案:");
               debugLog("方法1: 右键Flash Player → 全局设置");
               debugLog("      → 隐私 → 勾选'允许第三方Flash内容存储数据'");
               debugLog("      → 高级 → 勾选'允许访问本地文件'");
               debugLog("方法2: Flash Player设置管理器 → 添加游戏文件夹到信任位置");
               debugLog("方法3: 使用独立Flash Player运行游戏");
               debugLog("方法4: 使用下方的分类装备ID列表");
            }

            debugLog("🔧 备用方案 - 分类输出装备ID:");
            outputEquipmentIdsByCategory(content);
         }
      }

      // 分类输出装备ID
      private function outputEquipmentIdsByCategory(content:String):void
      {
         try {
            debugLog("=== 开始分类输出装备ID ===");

            var lines:Array = content.split("\n");
            var currentCategory:String = "";
            var categoryCount:int = 0;
            var itemCount:int = 0;

            for (var i:int = 0; i < lines.length; i++) {
               var line:String = lines[i];

               // 检测分类标题
               if (line.indexOf("【") >= 0 && line.indexOf("】") >= 0) {
                  if (categoryCount > 0) {
                     debugLog(""); // 分类间空行
                  }
                  currentCategory = line;
                  debugLog(line);
                  categoryCount++;
                  itemCount = 0;
               }
               // 输出装备ID行，但限制每个分类最多20个
               else if (line.indexOf(" - ") >= 0 && itemCount < 20) {
                  debugLog(line);
                  itemCount++;
               }
               // 如果超过20个，显示省略信息
               else if (line.indexOf(" - ") >= 0 && itemCount == 20) {
                  debugLog("... (该分类还有更多装备，请使用功能25查看完整列表)");
                  itemCount++;
               }

               // 限制总输出行数，避免控制台崩溃
               if (categoryCount >= 8) {
                  debugLog("... (还有更多分类，总共4423个装备)");
                  break;
               }
            }

            debugLog("");
            debugLog("=== 分类输出完成 ===");
            debugLog("💡 使用建议:");
            debugLog("1. 从上面选择需要的装备ID");
            debugLog("2. 使用功能1添加装备测试");
            debugLog("3. 推荐先测试: 10101001 (金箍棒)");

         } catch (e:Error) {
            debugLog("分类输出失败，改为输出常用装备ID:");
            outputSampleEquipmentIds();
         }
      }

      // 输出常用装备ID样本
      private function outputSampleEquipmentIds():void
      {
         debugLog("=== 常用装备ID列表 ===");
         debugLog("【武器类】");
         debugLog("10101001 - 金箍棒");
         debugLog("10101002 - 金箍棒+1");
         debugLog("10101003 - 金箍棒+2");
         debugLog("10104908 - 真·白虎圣剑");
         debugLog("10104009 - 深渊圣剑");
         debugLog("");
         debugLog("【防具类】");
         debugLog("10201001 - 布衣");
         debugLog("10201002 - 布衣+1");
         debugLog("10201003 - 布衣+2");
         debugLog("");
         debugLog("【饰品类】");
         debugLog("10301001 - 铜戒指");
         debugLog("10301002 - 铜戒指+1");
         debugLog("10301003 - 铜戒指+2");
         debugLog("");
         debugLog("【消耗品类】");
         debugLog("20001 - 小血瓶");
         debugLog("20002 - 中血瓶");
         debugLog("20003 - 大血瓶");
         debugLog("20011 - 小蓝瓶");
         debugLog("20012 - 中蓝瓶");
         debugLog("20013 - 大蓝瓶");
         debugLog("=== 装备ID列表结束 ===");
         debugLog("建议使用上述ID进行测试");
      }



      // 新增：检查装备ID是否在反作弊黑名单中
      private function isBlacklistedEquipmentId(equipmentId:int):Boolean
      {
         // 游戏反作弊系统的黑名单装备ID
         var blacklist1:Array = [10704014,10704114,10704214,10704314,10704414,10704514,10704614,10704714,10704814,10704914,10104014,10104114,10104214,10104314,10104414,10104514,10104614,10104714,10104814,10104914,10702014,10702114,10702214,10702314,10702414,10702514,10702614,10702714,10702814,10702914,10102014,10102114,10102214,10102314,10102414,10102514,10102614,10102714,10102814,10102914,10706014,10706114,10706214,10706314,10706414,10706514,10706614,10706714,10706814,10706914,10106014,10106114,10106214,10106314,10106414,10106514,10106614,10106714,10106814,10106914,10701014,10701114,10701214,10701314,10701414,10701514,10701614,10701714,10701814,10701914,10101014,10101114,10101214,10101314,10101414,10101514,10101614,10101714,10101814,10101914,10705014,10705114,10705214,10705314,10705414,10705514,10705614,10705714,10705814,10705914,10105014,10105114,10105214,10105314,10105414,10105514,10105614,10105714,10105814,10105914,10707014,10707114,10707214,10707314,10707414,10707514,10707614,10707714,10707814,10707914,10107014,10107114,10107214,10107314,10107414,10107514,10107614,10107714,10107814,10107914];

         var blacklist2:Array = [10200014,10200114,10200214,10200314,10200414,10200514,10200614,10200714,10200814,10200914];

         var blacklist3:Array = [10300014,10300114,10300214,10300314,10300414,10300514,10300614,10300714,10300814,10300914];

         return (blacklist1.indexOf(equipmentId) != -1 ||
                 blacklist2.indexOf(equipmentId) != -1 ||
                 blacklist3.indexOf(equipmentId) != -1);
      }

      // 新增：修复背包功能
      private function repairPackageFunction(player:Object):void
      {
         debugLog("开始修复背包功能");

         try {
            if (!player || !player.playerVO) {
               debugLog("错误：玩家或playerVO对象无效");
               return;
            }

            var repairCount:int = 0;

            // 修复装备背包
            if (player.playerVO.packageEquipmentVOs) {
               debugLog("检查装备背包，当前长度: " + player.playerVO.packageEquipmentVOs.length);

               // 确保背包数组不为null且有正确的长度
               if (player.playerVO.packageEquipmentVOs.length == 0) {
                  // 重新初始化背包数组
                  for (var i:int = 0; i < 50; i++) { // 假设背包有50个位置
                     player.playerVO.packageEquipmentVOs[i] = null;
                  }
                  debugLog("重新初始化装备背包，设置50个位置");
                  repairCount++;
               }

               // 清理损坏的装备对象
               for (var j:int = 0; j < player.playerVO.packageEquipmentVOs.length; j++) {
                  var equipment:Object = player.playerVO.packageEquipmentVOs[j];
                  if (equipment && (!equipment.id || equipment.id == 0)) {
                     player.playerVO.packageEquipmentVOs[j] = null;
                     debugLog("清理损坏的装备对象，位置: " + j);
                     repairCount++;
                  }
               }
            } else {
               debugLog("装备背包不存在，重新创建");
               player.playerVO.packageEquipmentVOs = new Array(50);
               for (var k:int = 0; k < 50; k++) {
                  player.playerVO.packageEquipmentVOs[k] = null;
               }
               repairCount++;
            }

            // 修复其他可能的背包
            if (player.playerVO.inforEquipmentVOs && player.playerVO.inforEquipmentVOs.length == 0) {
               for (var l:int = 0; l < 20; l++) { // 假设装备栏有20个位置
                  player.playerVO.inforEquipmentVOs[l] = null;
               }
               debugLog("修复装备栏");
               repairCount++;
            }

            if (player.playerVO.storageEquipmentVOs && player.playerVO.storageEquipmentVOs.length == 0) {
               for (var m:int = 0; m < 100; m++) { // 假设仓库有100个位置
                  player.playerVO.storageEquipmentVOs[m] = null;
               }
               debugLog("修复仓库");
               repairCount++;
            }

            debugLog("背包修复完成，修复项目数: " + repairCount);

            if (repairCount > 0) {
               refreshUI();
               debugLog("刷新UI完成");
            }

         } catch (e:Error) {
            debugLog("修复背包失败: " + e.message);
            debugLog("错误堆栈: " + e.getStackTrace());
         }
      }

      // 功能实现：添加装备
      private function addEquipmentFunction(player:Object, equipmentId:int, num:int):void
      {
         debugLog("开始添加装备功能，ID: " + equipmentId + ", 数量: " + num);

         try {
            if (!player) {
               debugLog("错误：玩家对象无效");
               return;
            }

            // 检查装备ID是否有效
            if (!isValidEquipmentId(equipmentId)) {
               debugLog("错误：装备ID无效，尝试使用替代装备ID");

               // 尝试一些已知的有效装备ID
               var validIds:Array = [10101001, 10201001, 10301001, 20001, 10500001];
               var foundValidId:int = -1;

               for (var j:int = 0; j < validIds.length; j++) {
                  if (isValidEquipmentId(validIds[j])) {
                     foundValidId = validIds[j];
                     debugLog("找到有效的替代装备ID: " + foundValidId);
                     break;
                  }
               }

               if (foundValidId != -1) {
                  debugLog("使用替代装备ID: " + foundValidId + " 代替无效ID: " + equipmentId);
                  equipmentId = foundValidId;
               } else {
                  debugLog("错误：无法找到任何有效的装备ID，列出可用装备ID:");
                  listAvailableEquipmentIds();
                  return;
               }
            }

            var count:int = num > 0 ? num : 1;
            var successCount:int = 0;

            for (var i:int = 0; i < count; i++) {
               debugLog("创建第 " + (i + 1) + " 个装备");

               var equipment:Object = createEquipment(equipmentId);
               if (equipment) {
                  if (addEquipmentToPackage(player, equipment)) {
                     successCount++;
                     debugLog("第 " + (i + 1) + " 个装备添加成功");
                  } else {
                     debugLog("第 " + (i + 1) + " 个装备添加失败");
                     break; // 如果背包满了，停止添加
                  }
               } else {
                  debugLog("第 " + (i + 1) + " 个装备创建失败");
                  break; // 如果创建失败，停止添加
               }
            }

            debugLog("装备添加完成，成功: " + successCount + "/" + count);

            if (successCount > 0) {
               refreshUI();
               debugLog("刷新UI完成");
            }

         } catch (e:Error) {
            debugLog("添加装备功能失败: " + e.message);
            debugLog("错误堆栈: " + e.getStackTrace());
         }
      }

      // 功能实现：添加衣服装备
      private function addClothesFunction(player:Object, clothesId:int, num:int):void
      {
         debugLog("开始添加衣服装备功能，ID: " + clothesId + ", 数量: " + num);

         try {
            if (!player) {
               debugLog("错误：玩家对象无效");
               return;
            }

            // 检查衣服ID是否有效
            if (!isValidEquipmentId(clothesId)) {
               debugLog("错误：衣服ID无效，请检查good.xml文件");
               return;
            }

            var count:int = num > 0 ? num : 1;
            var successCount:int = 0;

            for (var i:int = 0; i < count; i++) {
               debugLog("创建第 " + (i + 1) + " 个衣服装备");

               var clothes:Object = createEquipment(clothesId);
               if (clothes) {
                  if (addEquipmentToPackage(player, clothes)) {
                     successCount++;
                     debugLog("第 " + (i + 1) + " 个衣服装备添加成功");
                  } else {
                     debugLog("第 " + (i + 1) + " 个衣服装备添加失败");
                     break;
                  }
               } else {
                  debugLog("第 " + (i + 1) + " 个衣服装备创建失败");
                  break;
               }
            }

            debugLog("衣服装备添加完成，成功: " + successCount + "/" + count);

            if (successCount > 0) {
               refreshUI();
               debugLog("刷新UI完成");
            }

         } catch (e:Error) {
            debugLog("添加衣服装备功能失败: " + e.message);
            debugLog("错误堆栈: " + e.getStackTrace());
         }
      }

      // 功能实现：添加项链装备
      private function addNecklaceFunction(player:Object, necklaceId:int, num:int):void
      {
         debugLog("开始添加项链装备功能，ID: " + necklaceId + ", 数量: " + num);

         try {
            if (!player) {
               debugLog("错误：玩家对象无效");
               return;
            }

            // 检查项链ID是否有效
            if (!isValidEquipmentId(necklaceId)) {
               debugLog("错误：项链ID无效，请检查good.xml文件");
               return;
            }

            var count:int = num > 0 ? num : 1;
            var successCount:int = 0;

            for (var i:int = 0; i < count; i++) {
               debugLog("创建第 " + (i + 1) + " 个项链装备");

               var necklace:Object = createEquipment(necklaceId);
               if (necklace) {
                  if (addEquipmentToPackage(player, necklace)) {
                     successCount++;
                     debugLog("第 " + (i + 1) + " 个项链装备添加成功");
                  } else {
                     debugLog("第 " + (i + 1) + " 个项链装备添加失败");
                     break;
                  }
               } else {
                  debugLog("第 " + (i + 1) + " 个项链装备创建失败");
                  break;
               }
            }

            debugLog("项链装备添加完成，成功: " + successCount + "/" + count);

            if (successCount > 0) {
               refreshUI();
               debugLog("刷新UI完成");
            }

         } catch (e:Error) {
            debugLog("添加项链装备功能失败: " + e.message);
            debugLog("错误堆栈: " + e.getStackTrace());
         }
      }

      // 功能实现：添加葫芦装备
      private function addGourdFunction(player:Object, gourdId:int, num:int):void
      {
         debugLog("开始添加葫芦装备功能，ID: " + gourdId + ", 数量: " + num);

         try {
            if (!player) {
               debugLog("错误：玩家对象无效");
               return;
            }

            // 检查葫芦ID是否有效
            if (!isValidEquipmentId(gourdId)) {
               debugLog("错误：葫芦ID无效，请检查good.xml文件");
               return;
            }

            var count:int = num > 0 ? num : 1;
            var successCount:int = 0;

            for (var i:int = 0; i < count; i++) {
               debugLog("创建第 " + (i + 1) + " 个葫芦装备");

               var gourd:Object = createEquipment(gourdId);
               if (gourd) {
                  if (addEquipmentToPackage(player, gourd)) {
                     successCount++;
                     debugLog("第 " + (i + 1) + " 个葫芦装备添加成功");
                  } else {
                     debugLog("第 " + (i + 1) + " 个葫芦装备添加失败");
                     break;
                  }
               } else {
                  debugLog("第 " + (i + 1) + " 个葫芦装备创建失败");
                  break;
               }
            }

            debugLog("葫芦装备添加完成，成功: " + successCount + "/" + count);

            if (successCount > 0) {
               refreshUI();
               debugLog("刷新UI完成");
            }

         } catch (e:Error) {
            debugLog("添加葫芦装备功能失败: " + e.message);
            debugLog("错误堆栈: " + e.getStackTrace());
         }
      }

      // 功能实现：添加宝石
      private function addGemFunction(player:Object, gemId:int, num:int):void
      {
         debugLog("开始添加宝石功能，ID: " + gemId + ", 数量: " + num);

         try {
            if (!player) {
               debugLog("错误：玩家对象无效");
               return;
            }

            // 检查装备ID是否有效（宝石也是装备的一种）
            if (!isValidEquipmentId(gemId)) {
               debugLog("错误：宝石ID无效，请检查good.xml文件");
               return;
            }

            var count:int = num > 0 ? num : 1;
            var successCount:int = 0;

            for (var i:int = 0; i < count; i++) {
               debugLog("创建第 " + (i + 1) + " 个宝石");

               var gem:Object = createEquipment(gemId);
               if (gem) {
                  if (addEquipmentToPackage(player, gem)) {
                     successCount++;
                     debugLog("第 " + (i + 1) + " 个宝石添加成功");
                  } else {
                     debugLog("第 " + (i + 1) + " 个宝石添加失败");
                     break;
                  }
               } else {
                  debugLog("第 " + (i + 1) + " 个宝石创建失败");
                  break;
               }
            }

            debugLog("宝石添加完成，成功: " + successCount + "/" + count);

            if (successCount > 0) {
               refreshUI();
               debugLog("刷新UI完成");
            }

         } catch (e:Error) {
            debugLog("添加宝石功能失败: " + e.message);
            debugLog("错误堆栈: " + e.getStackTrace());
         }
      }

      // 功能实现：添加消耗品
      private function addConsumableFunction(player:Object, itemId:int, num:int):void
      {
         debugLog("开始添加消耗品功能，ID: " + itemId + ", 数量: " + num);

         try {
            if (!player) {
               debugLog("错误：玩家对象无效");
               return;
            }

            // 检查装备ID是否有效（消耗品也是装备的一种）
            if (!isValidEquipmentId(itemId)) {
               debugLog("错误：消耗品ID无效，请检查good.xml文件");
               return;
            }

            var count:int = num > 0 ? num : 1;
            var successCount:int = 0;

            for (var i:int = 0; i < count; i++) {
               debugLog("创建第 " + (i + 1) + " 个消耗品");

               var item:Object = createEquipment(itemId);
               if (item) {
                  if (addEquipmentToPackage(player, item)) {
                     successCount++;
                     debugLog("第 " + (i + 1) + " 个消耗品添加成功");
                  } else {
                     debugLog("第 " + (i + 1) + " 个消耗品添加失败");
                     break;
                  }
               } else {
                  debugLog("第 " + (i + 1) + " 个消耗品创建失败");
                  break;
               }
            }

            debugLog("消耗品添加完成，成功: " + successCount + "/" + count);

            if (successCount > 0) {
               refreshUI();
               debugLog("刷新UI完成");
            }

         } catch (e:Error) {
            debugLog("添加消耗品功能失败: " + e.message);
            debugLog("错误堆栈: " + e.getStackTrace());
         }
      }

      // 功能实现：添加其他道具
      private function addOtherItemFunction(player:Object, itemId:int, num:int):void
      {
         debugLog("开始添加其他道具功能，ID: " + itemId + ", 数量: " + num);

         try {
            if (!player) {
               debugLog("错误：玩家对象无效");
               return;
            }

            if (!isValidEquipmentId(itemId)) {
               debugLog("错误：道具ID无效，请检查good.xml文件");
               return;
            }

            var count:int = num > 0 ? num : 1;
            var successCount:int = 0;

            for (var i:int = 0; i < count; i++) {
               var item:Object = createEquipment(itemId);
               if (item) {
                  if (addEquipmentToPackage(player, item)) {
                     successCount++;
                  } else {
                     break;
                  }
               } else {
                  break;
               }
            }

            debugLog("其他道具添加完成，成功: " + successCount + "/" + count);

            if (successCount > 0) {
               refreshUI();
            }

         } catch (e:Error) {
            debugLog("添加其他道具功能失败: " + e.message);
         }
      }

      // 功能实现：添加任务道具
      private function addQuestItemFunction(player:Object, itemId:int, num:int):void
      {
         debugLog("开始添加任务道具功能，ID: " + itemId + ", 数量: " + num);

         try {
            if (!player) {
               debugLog("错误：玩家对象无效");
               return;
            }

            if (!isValidEquipmentId(itemId)) {
               debugLog("错误：任务道具ID无效，请检查good.xml文件");
               return;
            }

            var count:int = num > 0 ? num : 1;
            var successCount:int = 0;

            for (var i:int = 0; i < count; i++) {
               var item:Object = createEquipment(itemId);
               if (item) {
                  if (addEquipmentToPackage(player, item)) {
                     successCount++;
                  } else {
                     break;
                  }
               } else {
                  break;
               }
            }

            debugLog("任务道具添加完成，成功: " + successCount + "/" + count);

            if (successCount > 0) {
               refreshUI();
            }

         } catch (e:Error) {
            debugLog("添加任务道具功能失败: " + e.message);
         }
      }

      // 功能实现：添加宠物
      private function addPetFunction(player:Object, petId:int, level:int):void
      {
         debugLog("开始添加宠物功能，ID: " + petId + ", 等级: " + level);

         try {
            if (!player) {
               debugLog("错误：玩家对象无效");
               return;
            }

            if (!isValidEquipmentId(petId)) {
               debugLog("错误：宠物ID无效，请检查good.xml文件");
               return;
            }

            var pet:Object = createEquipment(petId);
            if (pet) {
               if (addEquipmentToPackage(player, pet)) {
                  debugLog("宠物添加成功");
                  refreshUI();
               } else {
                  debugLog("宠物添加失败");
               }
            } else {
               debugLog("宠物创建失败");
            }

         } catch (e:Error) {
            debugLog("添加宠物功能失败: " + e.message);
         }
      }

      // 功能实现：添加宠物装备
      private function addPetEquipmentFunction(player:Object, equipId:int, num:int):void
      {
         debugLog("开始添加宠物装备功能，ID: " + equipId + ", 数量: " + num);

         try {
            if (!player) {
               debugLog("错误：玩家对象无效");
               return;
            }

            if (!isValidEquipmentId(equipId)) {
               debugLog("错误：宠物装备ID无效，请检查good.xml文件");
               return;
            }

            var count:int = num > 0 ? num : 1;
            var successCount:int = 0;

            for (var i:int = 0; i < count; i++) {
               var equipment:Object = createEquipment(equipId);
               if (equipment) {
                  if (addEquipmentToPackage(player, equipment)) {
                     successCount++;
                  } else {
                     break;
                  }
               } else {
                  break;
               }
            }

            debugLog("宠物装备添加完成，成功: " + successCount + "/" + count);

            if (successCount > 0) {
               refreshUI();
            }

         } catch (e:Error) {
            debugLog("添加宠物装备功能失败: " + e.message);
         }
      }

      // 功能实现：清理背包
      private function clearPackageFunction(player:Object, type:String):void
      {
         debugLog("开始清理背包功能，类型: " + type);

         try {
            if (!player || !player.playerVO) {
               debugLog("错误：玩家或playerVO对象无效");
               return;
            }

            var packageArray:Object;
            var typeName:String;

            // 根据类型选择对应的背包
            switch (type) {
               case "equipment":
                  packageArray = player.playerVO.packageEquipmentVOs;
                  typeName = "武器装备";
                  break;
               case "clothes":
                  packageArray = player.playerVO.packageEquipmentVOs; // 衣服也存储在装备背包中
                  typeName = "衣服装备";
                  break;
               case "necklace":
                  packageArray = player.playerVO.packageEquipmentVOs; // 项链也存储在装备背包中
                  typeName = "项链装备";
                  break;
               case "gourd":
                  packageArray = player.playerVO.packageEquipmentVOs; // 葫芦也存储在装备背包中
                  typeName = "葫芦装备";
                  break;
               case "gem":
                  packageArray = player.playerVO.packageEquipmentVOs; // 宝石也存储在装备背包中
                  typeName = "宝石";
                  break;
               case "consumable":
                  packageArray = player.playerVO.packageEquipmentVOs; // 消耗品也存储在装备背包中
                  typeName = "消耗品";
                  break;
               case "other":
                  packageArray = player.playerVO.packageEquipmentVOs; // 其他道具也存储在装备背包中
                  typeName = "其他道具";
                  break;
               case "quest":
                  packageArray = player.playerVO.packageEquipmentVOs; // 任务道具也存储在装备背包中
                  typeName = "任务道具";
                  break;
               default:
                  packageArray = player.playerVO.packageEquipmentVOs;
                  typeName = "所有物品";
                  break;
            }

            if (!packageArray) {
               debugLog("错误：" + typeName + "背包不存在");
               return;
            }

            var clearCount:int = 0;
            var totalCount:int = packageArray.length;

            debugLog(typeName + "背包总容量: " + totalCount);

            // 清理所有物品
            for (var i:int = 0; i < totalCount; i++) {
               if (packageArray[i] != null) {
                  packageArray[i] = null;
                  clearCount++;
               }
            }

            debugLog(typeName + "背包清理完成，清理了 " + clearCount + " 个物品");

            if (clearCount > 0) {
               refreshUI();
               debugLog("刷新UI完成");
            }

         } catch (e:Error) {
            debugLog("清理背包功能失败: " + e.message);
            debugLog("错误堆栈: " + e.getStackTrace());
         }
      }









      // 功能实现：修改金币
      private function modifyMoneyFunction(player:Object, amount:int):void
      {
         debugLog("开始修改金币，目标值: " + amount);

         try {
            if (!player || !player.playerVO) {
               debugLog("错误：玩家或playerVO对象无效");
               return;
            }



            // 记录原始值
            var originalMoney:* = player.playerVO.money;
            debugLog("原始金币值: " + originalMoney + " (类型: " + typeof(originalMoney) + ")");

            // 修改金币
            var success:Boolean = false;

            // 方法1：直接修改
            try {
               player.playerVO.money = amount;
               debugLog("直接赋值成功");
               success = true;
            } catch (e1:Error) {
               debugLog("直接赋值失败: " + e1.message);
            }

            // 方法2：通过_antiwear修改
            if (!success && player.playerVO._antiwear) {
               try {
                  player.playerVO._antiwear.money = amount;
                  debugLog("通过_antiwear修改成功");
                  success = true;
               } catch (e2:Error) {
                  debugLog("通过_antiwear修改失败: " + e2.message);
               }
            }

            // 方法3：绕过加密修改
            if (!success) {
               try {
                  // 直接修改加密前的数据
                  if (player.playerVO._antiwear && player.playerVO._antiwear.data) {
                     player.playerVO._antiwear.data.money = amount;
                     debugLog("通过加密数据修改成功");
                     success = true;
                  }
               } catch (e3:Error) {
                  debugLog("通过加密数据修改失败: " + e3.message);
               }
            }

            // 验证修改结果
            var newMoney:* = player.playerVO.money;
            debugLog("修改后金币值: " + newMoney);

            if (success && newMoney == amount) {
               debugLog("金币修改成功！");
               refreshUI();
            } else {
               debugLog("金币修改失败，值未改变");
            }

         } catch (e:Error) {
            debugLog("修改金币失败: " + e.message);
            debugLog("错误堆栈: " + e.getStackTrace());
         }
      }



      // 功能实现：修改等级
      private function modifyLevelFunction(player:Object, level:int):void
      {
         debugLog("开始修改等级，目标值: " + level);

         try {
            if (!player || !player.playerVO) {
               debugLog("错误：玩家或playerVO对象无效");
               return;
            }

            var originalLevel:* = player.playerVO.level;
            debugLog("原始等级: " + originalLevel);

            // 尝试修改等级
            player.playerVO.level = level;

            var newLevel:* = player.playerVO.level;
            debugLog("修改后等级: " + newLevel);

            if (newLevel == level) {
               debugLog("等级修改成功！");
               refreshUI();
            } else {
               debugLog("等级修改失败，值未改变");
            }

         } catch (e:Error) {
            debugLog("修改等级失败: " + e.message);
            debugLog("错误堆栈: " + e.getStackTrace());
         }
      }

      // 功能实现：修改经验
      private function modifyExperienceFunction(player:Object, exp:int):void
      {
         debugLog("开始修改经验，目标值: " + exp);

         try {
            if (!player || !player.playerVO) {
               debugLog("错误：玩家或playerVO对象无效");
               return;
            }

            var originalExp:* = player.playerVO.experienceVolume;
            debugLog("原始经验: " + originalExp);

            player.playerVO.experienceVolume = exp;

            var newExp:* = player.playerVO.experienceVolume;
            debugLog("修改后经验: " + newExp);

            if (newExp == exp) {
               debugLog("经验修改成功！");
               refreshUI();
            } else {
               debugLog("经验修改失败，值未改变");
            }

         } catch (e:Error) {
            debugLog("修改经验失败: " + e.message);
            debugLog("错误堆栈: " + e.getStackTrace());
         }
      }

      // 功能实现：修改PK点（修复版）
      private function modifyPKPointFunction(player:Object, pkPoint:int):void
      {
         debugLog("开始修改PK点，目标值: " + pkPoint);

         try {
            if (!player) {
               debugLog("错误：玩家对象无效");
               return;
            }

            // 获取PKVO对象
            var pkVO:Object = null;
            if (player.getPKVO) {
               pkVO = player.getPKVO();
               debugLog("通过getPKVO()获取PKVO对象: " + (pkVO ? "成功" : "失败"));
            }

            if (!pkVO) {
               debugLog("PKVO对象为空，尝试创建新的PKVO对象");

               // 尝试创建新的PKVO对象
               try {
                  var PKVOClass:Class = getDefinitionByName("UI.Players.PKVO") as Class;
                  if (PKVOClass) {
                     pkVO = new PKVOClass();
                     debugLog("创建新PKVO对象成功");

                     // 设置到玩家对象
                     if (player.setPKVO) {
                        player.setPKVO(pkVO);
                        debugLog("将新PKVO对象设置到玩家成功");
                     }
                  }
               } catch (e0:Error) {
                  debugLog("创建PKVO对象失败: " + e0.message);
                  return;
               }
            }

            // 确保PKVO对象已初始化
            try {
               if (pkVO && pkVO._antiwear == null) {
                  debugLog("PKVO对象未初始化，尝试初始化");
                  if (pkVO.init) {
                     pkVO.init();
                     debugLog("PKVO对象初始化成功");
                  }
               }
            } catch (eInit:Error) {
               debugLog("PKVO初始化失败: " + eInit.message);
            }

            // 记录原始PK点
            var originalPKPoint:* = 0;
            try {
               if (pkVO.getPKPoint) {
                  originalPKPoint = pkVO.getPKPoint();
               } else if (pkVO._antiwear && pkVO._antiwear.pkPoint !== undefined) {
                  originalPKPoint = pkVO._antiwear.pkPoint;
               }
            } catch (e1:Error) {
               debugLog("获取原始PK点失败: " + e1.message);
            }
            debugLog("原始PK点: " + originalPKPoint);

            // 修改PK点 - 使用更直接的方法
            var success:Boolean = false;

            // 方法1：直接通过_antiwear修改（最可靠）
            try {
               if (pkVO._antiwear) {
                  pkVO._antiwear.pkPoint = pkPoint;
                  debugLog("通过_antiwear直接修改成功");
                  success = true;
               }
            } catch (e2:Error) {
               debugLog("通过_antiwear修改失败: " + e2.message);
            }

            // 方法2：通过setPKPoint方法修改
            if (!success) {
               try {
                  if (pkVO.setPKPoint) {
                     pkVO.setPKPoint(pkPoint);
                     debugLog("通过setPKPoint修改成功");
                     success = true;
                  }
               } catch (e3:Error) {
                  debugLog("通过setPKPoint修改失败: " + e3.message);
               }
            }

            // 方法3：强制设置私有变量（如果可能）
            if (!success) {
               try {
                  if (pkVO.hasOwnProperty("m_pkPoint")) {
                     pkVO["m_pkPoint"] = pkPoint;
                     debugLog("通过私有变量m_pkPoint修改成功");
                     success = true;
                  }
               } catch (e4:Error) {
                  debugLog("通过私有变量修改失败: " + e4.message);
               }
            }

            // 验证修改结果
            var newPKPoint:* = 0;
            try {
               if (pkVO.getPKPoint) {
                  newPKPoint = pkVO.getPKPoint();
               } else if (pkVO._antiwear && pkVO._antiwear.pkPoint !== undefined) {
                  newPKPoint = pkVO._antiwear.pkPoint;
               }
            } catch (e5:Error) {
               debugLog("获取新PK点失败: " + e5.message);
            }
            debugLog("修改后PK点: " + newPKPoint);

            if (success) {
               debugLog("PK点修改成功！从 " + originalPKPoint + " 改为 " + newPKPoint);
               refreshUI();

               // 强制保存游戏数据
               try {
                  if (myFunction2Class && myFunction2Class.saveGame) {
                     myFunction2Class.saveGame();
                     debugLog("游戏数据保存成功");
                  }
               } catch (e6:Error) {
                  debugLog("保存游戏数据失败: " + e6.message);
               }

            } else {
               debugLog("PK点修改失败，所有方法都无效");
            }

         } catch (e:Error) {
            debugLog("修改PK点失败: " + e.message);
            debugLog("错误堆栈: " + e.getStackTrace());
         }
      }

      // 新增：查找PK点显示位置的辅助函数
      private function findPKPointDisplay(player:Object):void
      {
         debugLog("=== 开始查找PK点显示位置 ===");

         try {
            // 检查玩家对象中的PK相关属性
            if (player && player.getPKVO) {
               var pkVO:Object = player.getPKVO();
               if (pkVO) {
                  debugLog("PKVO对象存在，当前PK点: " + pkVO.getPKPoint());
               }
            }

            // 检查游戏UI中是否有PK点显示
            if (gamingUI) {
               debugLog("正在检查GamingUI中的PK点显示...");

               // 尝试查找PK相关的UI组件
               try {
                  var uiChildren:* = gamingUI.numChildren;
                  debugLog("GamingUI子组件数量: " + uiChildren);

                  for (var i:int = 0; i < uiChildren; i++) {
                     var child:* = gamingUI.getChildAt(i);
                     if (child && child.name) {
                        var childName:String = String(child.name);
                        if (childName.toLowerCase().indexOf("pk") >= 0) {
                           debugLog("发现PK相关UI组件: " + childName);
                        }
                     }
                  }
               } catch (e1:Error) {
                  debugLog("检查UI组件失败: " + e1.message);
               }
            }

            // 检查是否需要打开特定界面才能看到PK点
            debugLog("=== PK点查看建议 ===");
            debugLog("1. 尝试打开角色信息面板");
            debugLog("2. 进入PK模式或竞技场");
            debugLog("3. 查看游戏设置或属性界面");
            debugLog("4. PK点可能只在PK相关功能中显示");

         } catch (e:Error) {
            debugLog("查找PK点显示位置失败: " + e.message);
         }

         debugLog("=== PK点显示位置查找完成 ===");
      }

      // 新增：尝试打开PK界面查看PK点
      private function openPKInterfaceFunction(player:Object):void
      {
         debugLog("=== 尝试打开PK界面查看PK点 ===");

         try {
            if (!player) {
               debugLog("错误：玩家对象无效");
               return;
            }

            // 首先显示当前PK点
            var pkVO:Object = null;
            if (player.getPKVO) {
               pkVO = player.getPKVO();
               if (pkVO && pkVO.getPKPoint) {
                  debugLog("当前PK点数值: " + pkVO.getPKPoint());
               }
            }

            // 尝试通过Part1打开PK界面
            try {
               if (part1Class && part1Class.getInstance) {
                  var part1Instance:Object = part1Class.getInstance();
                  if (part1Instance) {
                     debugLog("Part1实例获取成功，尝试打开PK界面");

                     // 尝试调用PK相关方法
                     if (part1Instance.openPK) {
                        part1Instance.openPK();
                        debugLog("通过openPK()方法打开PK界面成功");
                     } else if (part1Instance.showPK) {
                        part1Instance.showPK();
                        debugLog("通过showPK()方法打开PK界面成功");
                     } else {
                        debugLog("Part1中未找到PK界面打开方法");

                        // 列出Part1中的可用方法
                        debugLog("Part1可用方法:");
                        for (var prop:String in part1Instance) {
                           if (prop.toLowerCase().indexOf("pk") >= 0) {
                              debugLog("- " + prop);
                           }
                        }
                     }
                  }
               }
            } catch (e1:Error) {
               debugLog("通过Part1打开PK界面失败: " + e1.message);
            }

            // 尝试通过GamingUI打开相关界面
            try {
               if (gamingUI) {
                  debugLog("尝试通过GamingUI打开PK相关界面");

                  // 查找PK相关的UI方法
                  for (var method:String in gamingUI) {
                     if (method.toLowerCase().indexOf("pk") >= 0) {
                        debugLog("发现PK相关方法: " + method);
                        try {
                           if (gamingUI[method] is Function) {
                              debugLog("尝试调用: " + method);
                              gamingUI[method]();
                           }
                        } catch (e2:Error) {
                           debugLog("调用" + method + "失败: " + e2.message);
                        }
                     }
                  }
               }
            } catch (e3:Error) {
               debugLog("通过GamingUI打开界面失败: " + e3.message);
            }

            debugLog("=== PK界面打开尝试完成 ===");
            debugLog("提示：如果界面未打开，请手动进入游戏中的PK模式查看PK点");

         } catch (e:Error) {
            debugLog("打开PK界面功能失败: " + e.message);
            debugLog("错误堆栈: " + e.getStackTrace());
         }
      }









      // 功能实现：添加宝石
      private function addGemFunction(player:Object, gemId:int, num:int):void
      {
         try {
            var count:int = num > 0 ? num : 1;
            for (var i:int = 0; i < count; i++) {
               var gem:Object = createEquipment(gemId);
               if (gem) {
                  addEquipmentToPackage(player, gem);
               }
            }
            refreshUI();
            trace("成功添加宝石 ID:" + gemId + " 数量:" + count);
         } catch (e:Error) {
            trace("添加宝石失败: " + e.message);
         }
      }

      // 功能实现：添加消耗品
      private function addConsumableFunction(player:Object, itemId:int, num:int):void
      {
         try {
            var count:int = num > 0 ? num : 1;
            for (var i:int = 0; i < count; i++) {
               var item:Object = createEquipment(itemId);
               if (item) {
                  addEquipmentToPackage(player, item);
               }
            }
            refreshUI();
            trace("成功添加消耗品 ID:" + itemId + " 数量:" + count);
         } catch (e:Error) {
            trace("添加消耗品失败: " + e.message);
         }
      }

      // 功能实现：添加其他道具
      private function addOtherItemFunction(player:Object, itemId:int, num:int):void
      {
         try {
            var count:int = num > 0 ? num : 1;
            for (var i:int = 0; i < count; i++) {
               var item:Object = createEquipment(itemId);
               if (item) {
                  addEquipmentToPackage(player, item);
               }
            }
            refreshUI();
            trace("成功添加其他道具 ID:" + itemId + " 数量:" + count);
         } catch (e:Error) {
            trace("添加其他道具失败: " + e.message);
         }
      }

      // 功能实现：添加任务道具
      private function addQuestItemFunction(player:Object, itemId:int, num:int):void
      {
         try {
            var count:int = num > 0 ? num : 1;
            for (var i:int = 0; i < count; i++) {
               var item:Object = createEquipment(itemId);
               if (item) {
                  addEquipmentToPackage(player, item);
               }
            }
            refreshUI();
            trace("成功添加任务道具 ID:" + itemId + " 数量:" + count);
         } catch (e:Error) {
            trace("添加任务道具失败: " + e.message);
         }
      }

      // 功能实现：添加宠物
      private function addPetFunction(player:Object, petId:int, level:int):void
      {
         try {
            // 这里需要根据游戏的宠物系统来实现
            // 暂时使用通用的装备添加方法
            var pet:Object = createEquipment(petId);
            if (pet) {
               addEquipmentToPackage(player, pet);
            }
            refreshUI();
            trace("成功添加宠物 ID:" + petId + " 等级:" + level);
         } catch (e:Error) {
            trace("添加宠物失败: " + e.message);
         }
      }

      // 功能实现：添加宠物装备
      private function addPetEquipmentFunction(player:Object, equipId:int, num:int):void
      {
         try {
            var count:int = num > 0 ? num : 1;
            for (var i:int = 0; i < count; i++) {
               var equipment:Object = createEquipment(equipId);
               if (equipment) {
                  addEquipmentToPackage(player, equipment);
               }
            }
            refreshUI();
            trace("成功添加宠物装备 ID:" + equipId + " 数量:" + count);
         } catch (e:Error) {
            trace("添加宠物装备失败: " + e.message);
         }
      }







      // 辅助方法：刷新UI
      private function refreshUI():void
      {
         debugLog("开始刷新UI");

         try {
            if (!m_gamingUI) {
               debugLog("错误：GamingUI类未初始化");
               return;
            }

            var gamingUI:Object = m_gamingUI["getInstance"]();
            if (!gamingUI) {
               debugLog("错误：无法获取GamingUI实例");
               return;
            }

            // 尝试多种刷新方法
            var refreshMethods:Array = ["refresh", "refreshUI", "updateUI", "refreshAll"];
            var success:Boolean = false;

            for each (var method:String in refreshMethods) {
               try {
                  if (gamingUI[method] is Function) {
                     gamingUI[method](2); // 传入参数2表示刷新背包等UI
                     debugLog("通过" + method + "方法刷新UI成功");
                     success = true;
                     break;
                  }
               } catch (e1:Error) {
                  debugLog("通过" + method + "方法刷新UI失败: " + e1.message);
               }
            }

            if (!success) {
               debugLog("所有刷新方法都失败，尝试触发事件");
               try {
                  // 尝试触发刷新事件
                  gamingUI.dispatchEvent(new Event("refreshAtt"));
                  debugLog("触发refreshAtt事件成功");
               } catch (e2:Error) {
                  debugLog("触发refreshAtt事件失败: " + e2.message);
               }
            }

         } catch (e:Error) {
            debugLog("刷新UI失败: " + e.message);
            debugLog("错误堆栈: " + e.getStackTrace());
         }
      }

      internal function frame1() : *
      {
         this.conn = new LocalConnection();
         this.conn.client = this;
         this.m_l = new Loader();
         this.m_l.load(new URLRequest("game.swf"));
         this.m_l.contentLoaderInfo.addEventListener(Event.COMPLETE,this.__);
         Security.allowDomain("*");
         Cc.startOnStage(this,"");
         Cc.visible = true;
         Cc.commandLine = true;
         Cc.config.commandLineAllowed = true;
      }

      // 新增：解锁所有地图关卡功能
      private function unlockAllMapsFunction(player:Object):void
      {
         debugLog("=== 开始解锁所有地图关卡 ===");

         try {
            if (!player) {
               debugLog("错误：玩家对象无效");
               return;
            }

            var success:Boolean = false;
            var unlockCount:int = 0;

            // 方法1：通过LevelModeSaveData解锁普通地图
            try {
               var levelModeSaveDataClass:Class = getDefinitionByName("YJFY.LevelMode1.LevelModeSaveData") as Class;
               if (levelModeSaveDataClass && levelModeSaveDataClass.getInstance) {
                  var levelModeSaveData:Object = levelModeSaveDataClass.getInstance();
                  if (levelModeSaveData) {
                     debugLog("获取LevelModeSaveData实例成功");

                     // 解锁普通地图（设置为最大关卡数）
                     var maxMapLevel:int = 50; // 设置一个较大的数值
                     var maxGodLevel:int = 50;

                     // 获取当前关卡
                     var currentMapLevel:int = 0;
                     var currentGodLevel:int = 0;

                     try {
                        if (levelModeSaveData.getMapLevel) {
                           currentMapLevel = levelModeSaveData.getMapLevel();
                        }
                        if (levelModeSaveData.getGodLevel) {
                           currentGodLevel = levelModeSaveData.getGodLevel();
                        }
                     } catch (e1:Error) {
                        debugLog("获取当前关卡失败: " + e1.message);
                     }

                     debugLog("当前普通地图关卡: " + currentMapLevel);
                     debugLog("当前神级地图关卡: " + currentGodLevel);

                     // 设置地图关卡为最大值
                     try {
                        // 直接设置私有变量
                        if (levelModeSaveData._antiwear) {
                           levelModeSaveData._antiwear.mapLevel = maxMapLevel;
                           levelModeSaveData._antiwear.godLevel = maxGodLevel;
                           debugLog("通过_antiwear设置地图关卡成功");
                           success = true;
                           unlockCount += 2;
                        }
                     } catch (e2:Error) {
                        debugLog("通过_antiwear设置失败: " + e2.message);
                     }

                     // 尝试通过addOneMapLevel方法
                     if (!success) {
                        try {
                           for (var i:int = currentMapLevel; i < maxMapLevel; i++) {
                              if (levelModeSaveData.addOneMapLevel) {
                                 levelModeSaveData.addOneMapLevel();
                              }
                           }
                           for (var j:int = currentGodLevel; j < maxGodLevel; j++) {
                              if (levelModeSaveData.addOneGodLevel) {
                                 levelModeSaveData.addOneGodLevel();
                              }
                           }
                           debugLog("通过add方法解锁地图成功");
                           success = true;
                           unlockCount += 2;
                        } catch (e3:Error) {
                           debugLog("通过add方法解锁失败: " + e3.message);
                        }
                     }
                  }
               }
            } catch (e4:Error) {
               debugLog("获取LevelModeSaveData失败: " + e4.message);
            }

            // 方法2：解锁无尽模式关卡
            try {
               var endlessLevelDataClass:Class = getDefinitionByName("YJFY.EndlessMode.EndlessLevelData") as Class;
               if (endlessLevelDataClass && endlessLevelDataClass.getInstance) {
                  var endlessLevelData:Object = endlessLevelDataClass.getInstance();
                  if (endlessLevelData) {
                     debugLog("获取EndlessLevelData实例成功");

                     // 解锁所有无尽模式关卡
                     try {
                        var maxEndlessLevel:int = 20; // 无尽模式关卡数
                        for (var k:int = 0; k < maxEndlessLevel; k++) {
                           if (endlessLevelData.OpenLevel) {
                              endlessLevelData.OpenLevel(k, 1); // 玩家1
                              endlessLevelData.OpenLevel(k, 2); // 玩家2
                           }
                        }
                        debugLog("无尽模式关卡解锁成功");
                        unlockCount += maxEndlessLevel * 2;
                     } catch (e5:Error) {
                        debugLog("无尽模式解锁失败: " + e5.message);
                     }
                  }
               }
            } catch (e6:Error) {
               debugLog("获取EndlessLevelData失败: " + e6.message);
            }

            // 强制保存游戏数据
            try {
               if (myFunction2Class && myFunction2Class.saveGame) {
                  myFunction2Class.saveGame();
                  debugLog("游戏数据保存成功");
               }
            } catch (e10:Error) {
               debugLog("保存游戏数据失败: " + e10.message);
            }

            // 刷新UI
            refreshUI();

            debugLog("=== 地图解锁完成 ===");
            debugLog("成功解锁项目数: " + unlockCount);
            debugLog("提示：请重新进入地图选择界面查看解锁效果");

         } catch (e:Error) {
            debugLog("解锁地图功能失败: " + e.message);
            debugLog("错误堆栈: " + e.getStackTrace());
         }
      }
   }
}

